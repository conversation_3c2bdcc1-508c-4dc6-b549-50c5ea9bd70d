import sqlite3
from pathlib import Path

DB_PATH = Path('data/cookies.db')

def initialize_database():
    """Crea il database e le tabelle se non esistono"""
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        
        # Tabella OpenCookieDatabase
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS opencookiedb (
                id TEXT PRIMARY KEY,
                platform TEXT,
                category TEXT,
                cookie_name TEXT,
                domain TEXT,
                description TEXT,
                retention_period TEXT,
                data_controller TEXT,
                privacy_portals TEXT,
                wildcard_match INTEGER
            )
        ''')
        
        conn.commit()

def get_base_cookie_name(cookie_name):
    """Estrae la parte iniziale del nome del cookie prima del primo punto"""
    return cookie_name.split('.')[0]

def insert_cookie_if_not_exists(cookie_name, category, description, purpose, platform=None, domain=None):
    """
    Inserisce un nuovo cookie nella tabella opencookiedb se non esiste già.
    Utilizza il nome base del cookie per l'identificazione.
    """
    base_name = get_base_cookie_name(cookie_name)
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT OR IGNORE INTO opencookiedb (
                    id, platform, category, cookie_name, domain,
                    description, retention_period, data_controller,
                    privacy_portals, wildcard_match
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (base_name, platform, category, cookie_name, domain, description,
                  'Da determinare', 'Da determinare', 'Da determinare', 0))
            conn.commit()
            return cursor.lastrowid
        except sqlite3.IntegrityError:
            return None

def insert_opencookiedb_record(record):
    """
    Inserisce o aggiorna un record nella tabella opencookiedb
    
    Args:
        record (dict): Dizionario con i campi del record
        
    Returns:
        bool: True se l'inserimento/aggiornamento è riuscito, False altrimenti
    """
    required_fields = ['id', 'platform', 'category', 'cookie_name', 'domain',
                      'description', 'retention_period', 'data_controller',
                      'privacy_portals', 'wildcard_match']
    
    if not all(field in record for field in required_fields):
        return False
        
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        try:
            cursor.execute('''
                INSERT INTO opencookiedb (
                    id, platform, category, cookie_name, domain,
                    description, retention_period, data_controller,
                    privacy_portals, wildcard_match
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ON CONFLICT(id) DO UPDATE SET
                    platform = excluded.platform,
                    category = excluded.category,
                    cookie_name = excluded.cookie_name,
                    domain = excluded.domain,
                    description = excluded.description,
                    retention_period = excluded.retention_period,
                    data_controller = excluded.data_controller,
                    privacy_portals = excluded.privacy_portals,
                    wildcard_match = excluded.wildcard_match
            ''', (
                record['id'],
                record['platform'],
                record['category'],
                record['cookie_name'],
                record['domain'],
                record['description'],
                record['retention_period'],
                record['data_controller'],
                record['privacy_portals'],
                record['wildcard_match']
            ))
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
