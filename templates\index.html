<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body>
    <div class="container">
        <h1><PERSON><PERSON></h1>
        
        <form id="analyzeForm" class="mb-3">
            <div class="form-group">
                <input type="text" id="url" class="form-control" placeholder="Inserisci l'URL del sito web (https://www.esempio.com)" required>
            </div>
            <button type="submit" class="btn btn-primary">Analizza</button>
            
            {% if recent_urls %}
            <div class="mt-2">
                <p class="mb-1"><small>URL recenti:</small></p>
                <div class="d-flex flex-wrap">
                    {% for url in recent_urls %}
                    <button type="button" class="btn btn-sm btn-outline-secondary mr-1 mb-1 recent-url-btn" data-url="{{ url }}">{{ url }}</button>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </form>

        <div id="loading" class="alert alert-info" style="display:none;">Analisi dei cookie in corso...</div>
        
        <div id="results" class="results" style="display:none;">
            <h2>Risultati</h2>
            <p>Totale cookie trovati: <span id="cookieCount">0</span></p>
            <div id="cookieList"></div>
        </div>

        
        <a href="/reports" class="btn btn-secondary">Visualizza Report Precedenti</a>
    </div>

    <script>
        // Handle recent URL buttons
        document.querySelectorAll('.recent-url-btn').forEach(button => {
            button.addEventListener('click', function() {
                document.getElementById('url').value = this.getAttribute('data-url');
            });
        });
        
        document.getElementById('analyzeForm').onsubmit = async (e) => {
            e.preventDefault();
            
            const url = document.getElementById('url').value;
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            
            loading.style.display = 'block';
            results.style.display = 'none';
            
            try {
                const response = await fetch('/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ url }),
                });
                
                const data = await response.json();
                
                if (data.error) {
                    alert(data.error);
                    return;
                }
                
                document.getElementById('cookieCount').textContent = data.count;
                
                const cookieList = document.getElementById('cookieList');
                cookieList.innerHTML = '';
                
                data.cookies.forEach(cookie => {
                    cookieList.innerHTML += `
                        <p>
                            <strong>${cookie.name}</strong><br>
                            Dominio: ${cookie.domain}<br>
                            Percorso: ${cookie.path}<br>
                            Sicuro: ${cookie.secure}<br>
                            Solo HTTP: ${cookie.httpOnly}
                        </p>
                    `;
                });
                
                results.style.display = 'block';
            } catch (error) {
                alert('Errore durante l\'analisi dei cookie: ' + error.message);
            } finally {
                loading.style.display = 'none';
            }
        };
    </script>
</body>
</html>
