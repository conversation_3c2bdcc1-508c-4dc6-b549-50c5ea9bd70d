<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
</head>
<body>
    <div class="container">
        <h1><PERSON><PERSON></h1>
        
        <form id="analyzeForm" class="mb-3">
            <div class="form-group">
                <input type="text" id="url" class="form-control" placeholder="Inserisci l'URL del sito web (https://www.esempio.com)" required>
            </div>
            <button type="submit" class="btn btn-primary">Analizza</button>
            
            {% if recent_urls %}
            <div class="mt-2">
                <p class="mb-1"><small>URL recenti:</small></p>
                <div class="d-flex flex-wrap">
                    {% for url in recent_urls %}
                    <button type="button" class="btn btn-sm btn-outline-secondary mr-1 mb-1 recent-url-btn" data-url="{{ url }}">{{ url }}</button>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </form>

        <div id="loading" class="alert alert-info" style="display:none;">Analisi dei cookie in corso...</div>
        
        <div id="results" class="results" style="display:none;">
            <h2>Risultati</h2>
            <p>Totale cookie trovati: <span id="cookieCount">0</span></p>
            <div id="cookieList"></div>
        </div>

        
        <a href="/reports" class="btn btn-secondary">Visualizza Report Precedenti</a>
    </div>

    <script>
        // Handle recent URL buttons
        document.querySelectorAll('.recent-url-btn').forEach(button => {
            button.addEventListener('click', function() {
                document.getElementById('url').value = this.getAttribute('data-url');
            });
        });
        
        let currentSessionId = null;
        let statusCheckInterval = null;

        document.getElementById('analyzeForm').onsubmit = async (e) => {
            e.preventDefault();

            const url = document.getElementById('url').value;
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');

            loading.style.display = 'block';
            results.style.display = 'none';

            try {
                const response = await fetch('/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ url }),
                });

                const data = await response.json();

                if (data.error) {
                    alert(data.error);
                    return;
                }

                // Salva l'ID della sessione
                currentSessionId = data.session_id;

                // Mostra le istruzioni
                results.innerHTML = `
                    <div class="alert alert-info">
                        <h4>✅ Analisi avviata!</h4>
                        <p><strong>Sessione ID:</strong> ${data.session_id}</p>
                        <p><strong>Istruzioni:</strong></p>
                        <ol>
                            ${data.instructions.map(instruction => `<li>${instruction}</li>`).join('')}
                        </ol>
                        <p><a href="${data.vnc_url}" target="_blank" class="btn btn-primary">🖥️ Apri Browser VNC</a></p>
                        <div id="statusInfo">
                            <p><strong>Stato:</strong> <span id="statusText">Avvio in corso...</span></p>
                            <p><strong>Cookie raccolti:</strong> <span id="cookieCount">0</span></p>
                            <p><strong>Pagine visitate:</strong> <span id="pageCount">0</span></p>
                        </div>
                    </div>
                `;

                results.style.display = 'block';

                // Avvia il controllo dello stato
                startStatusCheck();

            } catch (error) {
                alert('Errore durante l\'avvio dell\'analisi: ' + error.message);
            } finally {
                loading.style.display = 'none';
            }
        };

        function startStatusCheck() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }

            statusCheckInterval = setInterval(async () => {
                if (!currentSessionId) return;

                try {
                    const response = await fetch(`/status/${currentSessionId}`);
                    const status = await response.json();

                    if (response.ok) {
                        document.getElementById('statusText').textContent = status.message;
                        document.getElementById('cookieCount').textContent = status.cookies_count;
                        document.getElementById('pageCount').textContent = status.pages_visited;

                        if (status.completed) {
                            clearInterval(statusCheckInterval);
                            document.getElementById('statusText').textContent = '✅ Analisi completata!';

                            // Aggiungi un pulsante per vedere i report
                            const statusInfo = document.getElementById('statusInfo');
                            statusInfo.innerHTML += `
                                <div class="mt-3">
                                    <a href="/reports" class="btn btn-success">📊 Visualizza Report</a>
                                    <button onclick="location.reload()" class="btn btn-secondary">🔄 Nuova Analisi</button>
                                </div>
                            `;
                        }
                    }
                } catch (error) {
                    console.error('Errore nel controllo dello stato:', error);
                }
            }, 2000); // Controlla ogni 2 secondi
        }
    </script>
</body>
</html>
