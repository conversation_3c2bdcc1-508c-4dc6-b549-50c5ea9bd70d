# Use an official Python runtime as a parent image
FROM python:3.9-slim

# Set the working directory in the container
WORKDIR /app

# Install system dependencies per Chrome, Selenium, VNC e noVNC (incluso git)
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    git \
    xvfb \
    x11vnc \
    libxi6 \
    libxss1 \
    libxtst6 \
    libnss3 \
    libatk-bridge2.0-0 \
    libgtk-3-0 \
    libx11-xcb1 \
    libxcb-dri3-0 \
    libdrm2 \
    libgbm1 \
    libasound2 \
    libxrandr2 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxfixes3 \
    libcairo-gobject2 \
    libxinerama1 \
    libpangocairo-1.0-0 \
    libatk1.0-0 \
    libcairo2 \
    libpango-1.0-0 \
    fonts-liberation \
    --no-install-recommends \
 && rm -rf /var/lib/apt/lists/*

# Install Google Chrome
RUN wget -q -O /tmp/google-chrome-stable.deb https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb \
 && apt-get update \
 && apt-get install -y /tmp/google-chrome-stable.deb --no-install-recommends \
 && rm -f /tmp/google-chrome-stable.deb \
 && rm -rf /var/lib/apt/lists/*

# Install noVNC
RUN git clone https://github.com/novnc/noVNC.git /opt/novnc \
    && git clone https://github.com/novnc/websockify /opt/novnc/utils/websockify \
    && ln -s /opt/novnc/vnc.html /opt/novnc/index.html

# Copy the requirements file into the container
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Installa chromedriver in fase di build (con privilegi di root)
RUN python -c "import chromedriver_autoinstaller; chromedriver_autoinstaller.install()"

# Forza i permessi di esecuzione per chromedriver_autoinstaller
RUN chmod -R 755 /usr/local/lib/python3.9/site-packages/chromedriver_autoinstaller

# Copy the entire project
COPY . .

# Copia lo script di entrypoint e rendilo eseguibile
COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Crea le directory necessarie
RUN mkdir -p /app/data /app/reports

# Crea un utente non-root e cambia la proprietà della directory /app
RUN useradd -ms /bin/bash appuser && chown -R appuser:appuser /app

# Imposta le variabili d'ambiente
ENV DISPLAY=:99
ENV PYTHONUNBUFFERED=1

# Espone le porte:
#  - 5000 per l'applicazione Flask
#  - 5900 per il server VNC (x11vnc)
#  - 6080 per noVNC
EXPOSE 5000 5900 6080

# Set Flask environment variables
ENV FLASK_APP=web_app.py
ENV FLASK_ENV=production

# Switch to non-root user
USER appuser

# Imposta l'entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]
