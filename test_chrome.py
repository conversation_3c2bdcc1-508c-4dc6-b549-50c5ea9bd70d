#!/usr/bin/env python3

import sys
import os
sys.path.append('/app')

from utils import setup_chrome_driver

def test_chrome():
    print("Testing Chrome setup...")
    
    try:
        driver = setup_chrome_driver()
        print("✅ Chrome driver created successfully")
        
        # Test basic navigation
        driver.get("https://www.google.com")
        print("✅ Successfully navigated to Google")
        
        # Get page title
        title = driver.title
        print(f"✅ Page title: {title}")
        
        # Test cookie functionality
        cookies = driver.get_cookies()
        print(f"✅ Found {len(cookies)} cookies")
        
        driver.quit()
        print("✅ Chrome test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Chrome test failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_chrome()
    sys.exit(0 if success else 1)
