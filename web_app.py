from flask import Flask, render_template, request, jsonify, send_from_directory, redirect, url_for
import shutil
import json
import os
import opencookiedb
from main import CookieAnalyzer
from pathlib import Path
import threading
import time

app = Flask(__name__)

# Dizionario globale per tenere traccia delle sessioni di analisi attive
active_sessions = {}

# Download and import cookie database on startup
print("Preparazione del database dei cookie...")
df = opencookiedb.download_cookie_database()
if df is not None:
    print(f"Database scaricato correttamente! Trovati {len(df)} cookie.")
    success, failed = opencookiedb.import_to_database(df)
    print(f"Importazione completata: {success} record importati, {failed} falliti")

# Ensure data directory exists
os.makedirs('data', exist_ok=True)

def get_recent_urls():
    """Get the list of recent URLs from the JSON file"""
    try:
        if os.path.exists('data/recent_urls.json'):
            with open('data/recent_urls.json', 'r') as f:
                data = json.load(f)
                return data.get('urls', [])
        return []
    except Exception:
        return []

def add_recent_url(url):
    """Add a URL to the recent URLs list and save to JSON"""
    urls = get_recent_urls()
    
    # Remove the URL if it already exists to avoid duplicates
    if url in urls:
        urls.remove(url)
    
    # Add the new URL at the beginning
    urls.insert(0, url)
    
    # Keep only the 5 most recent URLs
    urls = urls[:5]
    
    # Save to JSON
    with open('data/recent_urls.json', 'w') as f:
        json.dump({'urls': urls}, f)

@app.route('/')
def index():
    recent_urls = get_recent_urls()
    return render_template('index.html', recent_urls=recent_urls)

@app.route('/reports')
def list_reports():
    """Lista tutti i report disponibili"""
    reports_dir = Path('reports')
    if not reports_dir.exists():
        return render_template('reports.html', domains=[])
    
    # Struttura dati per organizzare i report
    domains = {}
    
    # Scansiona la directory dei report
    for domain_dir in reports_dir.iterdir():
        if domain_dir.is_dir():
            domain_name = domain_dir.name
            domains[domain_name] = []
            
            # Per ogni dominio, trova tutte le sottodirectory con timestamp
            timestamp_dirs = sorted(
                [d for d in domain_dir.iterdir() if d.is_dir()],
                key=lambda x: x.name,
                reverse=True  # Ordine decrescente
            )
            for timestamp_dir in timestamp_dirs:
                if timestamp_dir.is_dir():
                    # Trova tutti i file di report in questa directory
                    reports = []
                    for report_file in timestamp_dir.glob('*.*'):
                        if report_file.suffix in ['.csv', '.md', '.html']:
                            reports.append({
                                'filename': report_file.name,
                                'path': f"{domain_name}/{timestamp_dir.name}/{report_file.name}",
                                'type': report_file.suffix[1:].upper(),
                                'timestamp': timestamp_dir.name
                            })
                    if reports:
                        domains[domain_name].append({
                            'timestamp': timestamp_dir.name,
                            'reports': reports
                        })
    
    return render_template('reports.html', domains=domains)

@app.route('/reports/<path:filename>')
def serve_report(filename):
    """Serve i file dei report"""
    return send_from_directory('reports', filename)

@app.route('/analyze', methods=['POST'])
def analyze():
    url = request.json.get('url')
    if not url:
        return jsonify({'error': 'URL is required'}), 400

    # Add the URL to recent URLs
    add_recent_url(url)

    try:
        analyzer = CookieAnalyzer(url)
        analyzer.start_navigation()

        # Genera un ID di sessione unico
        session_id = str(time.time()).replace('.', '')

        # Avvia l'analisi in modalità non bloccante
        analyzer.navigate_interactively(non_blocking=True)

        # Salva l'analyzer nella sessione attiva
        active_sessions[session_id] = analyzer

        return jsonify({
            'success': True,
            'session_id': session_id,
            'message': 'Analisi avviata! Naviga nel browser VNC. I cookie verranno raccolti automaticamente.',
            'instructions': [
                '1. Apri il browser VNC su http://localhost:6080/vnc.html',
                '2. Naviga nel sito web come faresti normalmente',
                '3. I cookie vengono raccolti automaticamente durante la navigazione',
                '4. Quando hai finito, chiudi semplicemente il browser',
                '5. L\'analisi si completerà automaticamente e i report verranno generati'
            ],
            'vnc_url': 'http://localhost:6080/vnc.html'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/status/<session_id>', methods=['GET'])
def get_status(session_id):
    """Controlla lo stato di un'analisi in corso"""
    if session_id not in active_sessions:
        return jsonify({'error': 'Session not found'}), 404

    analyzer = active_sessions[session_id]
    status = analyzer.get_status()

    # Se l'analisi non è più attiva, rimuovi la sessione
    if not status['active']:
        # Pulisci la sessione
        try:
            analyzer.close()
        except:
            pass
        del active_sessions[session_id]
        status['completed'] = True
        status['message'] = 'Analisi completata! I report sono stati generati.'
    else:
        status['completed'] = False
        status['message'] = 'Analisi in corso...'

    return jsonify(status)

@app.route('/delete_analysis/<domain>/<timestamp>', methods=['POST'])
def delete_analysis(domain, timestamp):
    """Elimina la directory dell'analisi specificata"""
    try:
        analysis_path = Path('reports') / domain / timestamp
        if analysis_path.exists():
            shutil.rmtree(analysis_path)
            
            # Se la directory del dominio è vuota, eliminala
            domain_path = Path('reports') / domain
            if not any(domain_path.iterdir()):
                domain_path.rmdir()
                
        return redirect(url_for('list_reports'))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/delete_all_reports', methods=['POST'])
def delete_all_reports():
    """Elimina tutti i report"""
    try:
        reports_dir = Path('reports')
        if reports_dir.exists():
            # Elimina tutte le sottodirectory ma mantiene la directory reports
            for item in reports_dir.iterdir():
                if item.is_dir():
                    shutil.rmtree(item)
                elif item.is_file():
                    item.unlink()
        return redirect(url_for('list_reports'))
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0')
