from openai import OpenAI
import json
import os
from dotenv import load_dotenv

# Carica le variabili d'ambiente
load_dotenv()

# Configura OpenAI
client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

def analyze_cookie(cookie_name):
    """Analizza un cookie usando ChatGPT e restituisce le informazioni in formato JSON."""
    prompt = f"""Analizza il seguente cookie e restituisci le informazioni richieste in formato JSON.
Cookie Name: {cookie_name}

Il JSON deve contenere esattamente i seguenti campi:
- "cookie_name": il nome del cookie.
- "platform": la piattaforma o il sistema a cui è associato il cookie (es. "Wordpress", "Woocommerce", "Joomla", "Google Analytics", ecc.). Se non è possibile identificarla, lascia una stringa vuota.
- "purpose": lo scopo principale del cookie (massimo 30 parole).
- "category": la categoria del cookie (tecnico, analitico, marketing, funzionale, ecc.).
- "description": una breve descrizione del cookie (massimo 50 parole).

Assicurati che il JSON sia valido e non includa ulteriori proprietà.

Esempio di output:
{{
    "cookie_name": "_wpfuuid",
    "platform": "Wordpress",
    "purpose": "Identificatore univoco per tracciare i visitatori e gestire i moduli inviati tramite WPForms.",
    "category": "Funzionale",
    "description": "Cookie usato dal plugin WPForms per assegnare un identificatore univoco ai visitatori e mantenere la sessione attiva."
}}"""

    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "Sei un esperto di cookie e privacy online. Rispondi esclusivamente in formato JSON, seguendo lo schema fornito."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=200,
            response_format={
                "type": "json_schema",
                "json_schema": {
                    "name": "cookie_analysis",
                    "strict": True,
                    "schema": {
                        "type": "object",
                        "properties": {
                            "cookie_name": {"type": "string"},
                            "platform": {"type": "string"},
                            "purpose": {"type": "string"},
                            "category": {"type": "string"},
                            "description": {"type": "string"}
                        },
                        "required": ["cookie_name", "platform", "purpose", "category", "description"],
                        "additionalProperties": False
                    }
                }
            }
        )
        
        content = response.choices[0].message.content
        return json.loads(content.strip())
    
    except Exception as e:
        print(f"Errore durante l'analisi del cookie: {str(e)}")
        print(f"Content: {content}")
        return {
            "cookie_name": "sconosciuto",
            "platform": "",
            "purpose": "Da determinare",
            "category": "Da determinare",
            "description": "Non è stato possibile analizzare questo cookie."
        }

if __name__ == "__main__":
    # Modalità interattiva per testare il modulo
    print("Analizzatore di cookie con ChatGPT")
    while True:
        cookie_name = input("\nInserisci il nome di un cookie (o 'exit' per uscire): ").strip()
        if cookie_name.lower() == 'exit':
            break
            
        print(f"\nAnalisi del cookie: {cookie_name}")
        result = analyze_cookie(cookie_name)
        
        print("\nRisultato JSON:")
        print(json.dumps(result, indent=4, ensure_ascii=False))
