from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from utils import setup_driver, check_cookie_banner, collect_cookies
from report_generator import generate_reports
from database import initialize_database, insert_cookie_if_not_exists, get_base_cookie_name
from pathlib import Path
import argparse
import opencookiedb

# Crea la cartella data se non esiste
Path("data").mkdir(exist_ok=True)
import time
import pandas as pd

class CookieAnalyzer:
    def __init__(self, url):
        self.url = url
        self.driver = setup_driver()
        self.cookies_data = []
        self.actions_log = []
        self.visited_pages = set()
        # Inizializza il database
        initialize_database()

    def start_navigation(self):
        print(f"Navigando verso: {self.url}")
        try:
            self.driver.get(self.url)
            self.visited_pages.add(self.url)
            self.actions_log.append(f"Visited: {self.url}")

            # Attendi il caricamento completo della pagina
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, 'body'))
            )

            # Raccogli i cookie iniziali
            self.collect_cookies()
            print(f"Cookie iniziali raccolti: {len(self.cookies_data)}")

            # Log dei cookie raccolti nella pagina iniziale
            self.actions_log.append(f"Collected {len(self.cookies_data)} cookies from initial page: {self.driver.current_url}")

        except Exception as e:
            print(f"Errore durante la navigazione iniziale: {str(e)}")
            self.actions_log.append(f"Error during initial navigation: {str(e)}")
            # Non interrompere l'analisi, continua comunque

        # Cerca iframe e raccogli i cookie dagli iframe nella pagina iniziale
        self.collect_iframe_cookies()

    def collect_cookies(self):
        """Raccoglie i cookie dalla pagina corrente e li aggiunge alla lista evitando duplicati"""
        try:
            new_cookies = collect_cookies(self.driver)
            unique_cookies = []

            for cookie in new_cookies:
                # Crea una chiave unica basata su nome, dominio e percorso
                cookie_key = (cookie['name'], cookie['domain'], cookie['path'])

                # Verifica se il cookie è già presente
                if not any((c['name'], c['domain'], c['path']) == cookie_key for c in self.cookies_data):
                    self.cookies_data.append(cookie)
                    unique_cookies.append(cookie)

            if unique_cookies:
                print(f"Raccolti {len(unique_cookies)} nuovi cookie da {self.driver.current_url}")
                for cookie in unique_cookies:
                    print(f"  - {cookie['name']} (dominio: {cookie['domain']})")

            self.actions_log.append(f"Collected {len(unique_cookies)} unique cookies from {self.driver.current_url}")

        except Exception as e:
            print(f"Errore durante la raccolta dei cookie: {str(e)}")
            self.actions_log.append(f"Error collecting cookies: {str(e)}")

    def collect_iframe_cookies(self):
        """Raccoglie i cookie dagli iframe incorporati"""
        try:
            iframes = self.driver.find_elements(By.TAG_NAME, 'iframe')
            for iframe in iframes:
                try:
                    # Passa al contesto dell'iframe
                    self.driver.switch_to.frame(iframe)
                    # Raccogli i cookie
                    self.collect_cookies()
                    # Torna al contesto principale
                    self.driver.switch_to.default_content()
                except Exception as e:
                    print(f"Errore durante la raccolta dei cookie da iframe: {str(e)}")
                    self.driver.switch_to.default_content()
        except Exception as e:
            print(f"Errore generale nella ricerca di iframe: {str(e)}")

    def navigate_interactively(self, non_blocking=False):
        print("\nInizio navigazione automatica...")

        # Funzione per monitorare i cambiamenti di URL
        def monitor_navigation():
            last_url = self.driver.current_url
            while getattr(self, 'driver', None) and self.driver and not getattr(self, 'stop_navigation_flag', False):
                try:
                    current_url = self.driver.current_url

                    if current_url != last_url:
                        last_url = current_url
                        if current_url not in self.visited_pages:
                            self.visited_pages.add(current_url)
                            self.actions_log.append(f"Navigated to: {current_url}")
                            print(f"Pagina caricata: {current_url}")
                            print(f"Cookie rilevati finora: {len(self.cookies_data)}")
                        self.collect_cookies()
                        self.collect_iframe_cookies()

                    WebDriverWait(self.driver, 1).until(
                        lambda d: d.execute_script('return document.readyState') == 'complete'
                    )

                except Exception as e:
                    # Gestisci la chiusura del browser in modo silenzioso
                    if any(error in str(e) for error in [
                        'NoSuchSessionException',
                        'invalid session id',
                        'Connection aborted',
                        'Connection refused',
                        'Remote end closed connection'
                    ]):
                        print("\nBrowser chiuso. Terminazione del monitoraggio...")
                        # Elabora i cookie un'ultima volta prima di terminare
                        try:
                            self.collect_cookies()
                            self.collect_iframe_cookies()
                        except:
                            pass
                        print("Elaborazione finale completata.")
                        # Genera i report e salva nel database quando il browser viene chiuso
                        try:
                            self.generate_reports()
                            self.save_cookies_to_db()
                            print(f"Analisi completata! Raccolti {len(self.cookies_data)} cookie.")
                        except Exception as report_error:
                            print(f"Errore durante la generazione dei report: {report_error}")
                        break
                    else:
                        print(f"Errore durante il monitoraggio: {type(e).__name__}")
                    break

                time.sleep(0.5)

        # Avvia il monitoraggio in un thread separato
        import threading
        self.monitor_thread = threading.Thread(target=monitor_navigation)
        self.monitor_thread.start()

        print("\nMonitoraggio della navigazione avviato in background.")

        if non_blocking:
            print("Modalità non bloccante: il monitoraggio continuerà in background.")
            return

        print("Digita 'x' nel terminale e premi Invio per interrompere l'analisi o chiudi la finestra del browser.")
        self.show_current_cookies()

        import sys, select
        # Keep main thread alive while monitoring using non-blocking input
        while self.monitor_thread.is_alive():
            # Attendi per un massimo di 0.5 secondi per vedere se c'è input da tastiera
            rlist, _, _ = select.select([sys.stdin], [], [], 0.5)
            if rlist:
                user_input = sys.stdin.readline().strip()
                if user_input.lower() == 'x':
                    print("Ricevuto comando di interruzione. Attendere la terminazione...")
                    self.stop_navigation_flag = True  # Imposta il flag per far terminare il thread
                    break

        self.monitor_thread.join()  # Attendi che il thread di monitoraggio termini

    def show_current_cookies(self):
        current_cookies = collect_cookies(self.driver)
        print(f"\nCookie attuali ({len(current_cookies)} trovati):")
        for cookie in current_cookies:
            print(f"- {cookie['name']} (Dominio: {cookie['domain']}, Terze parti: {'Sì' if cookie['is_third_party'] else 'No'})")

    def is_active(self):
        """Controlla se l'analisi è ancora attiva"""
        return hasattr(self, 'monitor_thread') and self.monitor_thread.is_alive()

    def get_status(self):
        """Restituisce lo stato corrente dell'analisi"""
        return {
            'active': self.is_active(),
            'cookies_count': len(self.cookies_data),
            'pages_visited': len(self.visited_pages),
            'current_url': self.driver.current_url if hasattr(self, 'driver') and self.driver else None
        }

    def generate_reports(self):
        generate_reports(self.cookies_data, self.actions_log, self.visited_pages, self.url)

    def save_cookies_to_db(self):
        """Salva i cookie unici nel database"""
        unique_cookies = set()

        for cookie in self.cookies_data:
            base_name = get_base_cookie_name(cookie['name'])
            cookie_key = (base_name, cookie['domain'])
            if cookie_key not in unique_cookies:
                insert_cookie_if_not_exists(
                    cookie['name'],
                    "Da classificare",
                    "Nessuna descrizione disponibile.",
                    "Da determinare",
                    cookie['domain']
                )
                unique_cookies.add(cookie_key)

        self.actions_log.append(f"Saved {len(unique_cookies)} unique cookies to database")

    def close(self):
        if hasattr(self, 'driver') and self.driver:
            # Chiudi il driver in modo pulito e gestisci le eccezioni se il browser è già chiuso
            try:
                self.driver.quit()
            except Exception as e:
                print(f"Errore durante la chiusura del browser: {str(e)}")
            self.driver = None
        # Wait for monitoring thread to finish if it exists
        if hasattr(self, 'monitor_thread') and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1)
        print("Browser chiuso correttamente")

if __name__ == "__main__":
    # Prima di tutto, scarica e importa il database dei cookie
    print("Preparazione del database dei cookie...")
    df = opencookiedb.download_cookie_database()
    if df is not None:
        print(f"Database scaricato correttamente! Trovati {len(df)} cookie.")
        success, failed = opencookiedb.import_to_database(df)
        print(f"Importazione completata: {success} record importati, {failed} falliti")

    parser = argparse.ArgumentParser(description="Analizzatore di cookie per siti web.")
    parser.add_argument("url", nargs='?', help="URL del sito da analizzare")
    
    args = parser.parse_args()
    
    if args.url:
        url = args.url
    else:
        url = input("Inserisci l'URL del sito da analizzare: ")
    analyzer = CookieAnalyzer(url)
    
    try:
        analyzer.start_navigation()
        analyzer.navigate_interactively()
        
        # Generate reports and save cookies after monitoring ends
        analyzer.generate_reports()
        analyzer.save_cookies_to_db()
    except KeyboardInterrupt:
        print("\nAnalisi interrotta dall'utente")
    finally:
        analyzer.close()
