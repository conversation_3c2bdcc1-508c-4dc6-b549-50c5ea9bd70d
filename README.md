# Cookie Analyzer

## Descrizione del Progetto

Questo progetto è uno strumento per l'analisi dei cookie dei siti web. Utilizza Selenium per navigare i siti web, intercettare e raccogliere i cookie, e genera report dettagliati sui cookie trovati. Può anche interagire con OpenAI per analizzare i cookie sconosciuti e arricchire i report.

## Dipendenze

Assicurati di aver installato le seguenti dipendenze prima di eseguire il progetto:

```bash
pip install -r requirements.txt
```

## Configurazione

1. **Clona il repository:**
   ```bash
   git clone https://gitlab.prismanet.com/prismanet/cookie-analyzer.git
   cd cookie-analyzer
   ```

2. **Crea e attiva l'ambiente Conda (opzionale ma consigliato):**
   ```bash
   conda create --name cookie-analyzer python=3.9
   conda activate cookie-analyzer
   ```

3. **Installa le dipendenze:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Scarica e configura il database dei cookie (OpenCookieDatabase):**

   Prima di eseguire l'analisi dei cookie, è necessario scaricare il database OpenCookieDatabase, che contiene informazioni sui cookie noti. Questo database viene utilizzato per identificare e classificare i cookie trovati durante l'analisi.

   Esegui lo script `opencookiedb.py`:

   ```bash
   python opencookiedb.py
   ```

   Questo script scaricherà il database e lo configurerà per l'utilizzo.  Puoi eseguire questo script periodicamente per aggiornare il database con le ultime informazioni sui cookie.

5. **Installa il browser (Chrome) necessario per Selenium:**

   Questo progetto utilizza Google Chrome come browser predefinito. Se non lo hai già installato, segui questi passaggi.

   **Per sistemi basati su Debian/Ubuntu:**

   ```bash
   wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | gpg --dearmor | sudo tee /usr/share/keyrings/google-chrome.gpg > /dev/null
   echo "deb [arch=amd64 signed-by=/usr/share/keyrings/google-chrome.gpg] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
   sudo apt update
   sudo apt install google-chrome-stable
   ```

   **Note sull'installazione del browser:**

   *  Le istruzioni sopra riportate installano l'ultima versione stabile di Google Chrome.
   *  Assicurati di aver configurato correttamente i repository del tuo sistema prima di installare Chrome.
   *  Se hai bisogno di un browser diverso, dovrai modificare la configurazione di Selenium nel codice sorgente per utilizzare il browser desiderato.

6. **Configurazione delle variabili d'ambiente:**
   - Copia il file `.env.example` in `.env`:
     ```bash
     cp .env.example .env
     ```
   - Modifica il file `.env` con le tue credenziali:
     - Inserisci la tua chiave API di OpenAI
     - Configura le credenziali del database se necessario

## Esecuzione

Per avviare l'analisi di un sito web, esegui lo script `main.py` fornendo l'URL del sito come argomento:

```bash
python main.py <url_del_sito_da_analizzare>
```

Se non fornisci un URL come argomento, ti verrà richiesto di inserirlo all'avvio dello script.

## Note sull'ambiente WSL

Se stai eseguendo il progetto in un ambiente WSL (Windows Subsystem for Linux) e riscontri problemi con la visualizzazione dell'interfaccia grafica del browser, potrebbe essere necessario impostare le seguenti variabili d'ambiente nel tuo terminale WSL prima di eseguire lo script:

```bash
export DISPLAY=$(hostname -I | awk '{print $1}'):0.0
export LIBGL_ALWAYS_INDIRECT=1
```

Queste variabili sono necessarie per reindirizzare la visualizzazione delle applicazioni grafiche.