#!/bin/bash
set -e

# Pulisci eventuali lock file esistenti
echo "Cleaning up X11 lock files..."
rm -f /tmp/.X99-lock
rm -rf /tmp/.X11-unix

# Crea la directory X11-unix se non esiste
mkdir -p /tmp/.X11-unix
chmod 1777 /tmp/.X11-unix

# Avvia Xvfb in background
echo "Starting Xvfb..."
Xvfb :99 -screen 0 1280x800x24 -ac +extension GLX +render -noreset &
XVFB_PID=$!

# Aspetta che Xvfb sia pronto
echo "Waiting for Xvfb to be ready..."
for i in {1..10}; do
    if xdpyinfo -display :99 >/dev/null 2>&1; then
        echo "Xvfb is ready!"
        break
    fi
    echo "Waiting for Xvfb... ($i/10)"
    sleep 1
done

# Verifica che Xvfb sia effettivamente in esecuzione
if ! xdpyinfo -display :99 >/dev/null 2>&1; then
    echo "ERROR: Xvfb failed to start properly"
    exit 1
fi

# Avvia x11vnc in background, collegandosi al display :99
echo "Starting x11vnc..."
x11vnc -display :99 -forever -nopw -listen 0.0.0.0 &

# Avvia noVNC in background per esporre il VNC tramite web
echo "Starting noVNC..."
/opt/novnc/utils/novnc_proxy --vnc localhost:5900 --listen 6080 &

# Aspetta un momento per assicurarsi che i servizi siano avviati
sleep 3

# Avvia l'applicazione Python
echo "Starting Flask application..."
python web_app.py
