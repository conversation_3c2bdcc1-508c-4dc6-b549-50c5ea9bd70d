#!/bin/bash
set -e

# Avvia Xvfb in background
echo "Starting Xvfb..."
Xvfb :99 -screen 0 1280x800x24 &
sleep 2

# Avvia x11vnc in background, collegandosi al display :99
echo "Starting x11vnc..."
x11vnc -display :99 -forever -nopw -listen 0.0.0.0 &

# Avvia noVNC in background per esporre il VNC tramite web
echo "Starting noVNC..."
/opt/novnc/utils/novnc_proxy --vnc localhost:5900 --listen 6080 &

# Aspetta un momento per assicurarsi che i servizi siano avviati
sleep 3

# Avvia l'applicazione Python
echo "Starting Flask application..."
python web_app.py
