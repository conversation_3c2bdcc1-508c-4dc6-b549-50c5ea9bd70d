import requests
import pandas as pd
from pathlib import Path
import database
import time

def is_file_older_than(filepath, days):
    """
    Verifica se un file è più vecchio di un certo numero di giorni
    
    Args:
        filepath (Path): Percorso del file
        days (int): Numero di giorni
        
    Returns:
        bool: True se il file è più vecchio o non esiste, False altrimenti
    """
    if not filepath.exists():
        return True
    file_age = (time.time() - filepath.stat().st_mtime) / (60 * 60 * 24)
    return file_age > days

def download_cookie_database():
    """
    Scarica il database dei cookie da OpenCookieDatabase
    e lo salva in un file CSV nella root del progetto.
    Scarica solo se il file non esiste o è più vecchio di 7 giorni.
    
    Returns:
        pd.DataFrame: DataFrame con i dati dei cookie
    """
    url = "https://raw.githubusercontent.com/jkwakman/Open-Cookie-Database/refs/heads/master/open-cookie-database.csv"
    
    # Percorso del file locale
    local_path = Path("data/open-cookiedb.csv")
    
    # Se il file esiste ed è recente, carica direttamente
    if not is_file_older_than(local_path, 7):
        print("Trovato file locale aggiornato, caricamento...")
        return pd.read_csv(local_path)
    
    try:
        # Scarica il file
        response = requests.get(url)
        response.raise_for_status()
        
        # Salva il file localmente
        with open(local_path, 'wb') as f:
            f.write(response.content)
            
        # Carica il CSV in un DataFrame
        df = pd.read_csv(local_path)
        return df
        
    except requests.exceptions.RequestException as e:
        print(f"Errore durante il download del database: {e}")
        return None

def import_to_database(df):
    """
    Importa i dati dal DataFrame nel database
    
    Args:
        df (pd.DataFrame): DataFrame con i dati dei cookie
        
    Returns:
        tuple: (num_success, num_failed) numero di record importati con successo e falliti
    """
    num_success = 0
    num_failed = 0
    
    # Inizializza il database
    database.initialize_database()
    
    # Itera sulle righe del DataFrame
    for _, row in df.iterrows():
        record = {
            'id': row['ID'],
            'platform': row['Platform'],
            'category': row['Category'],
            'cookie_name': row['Cookie / Data Key name'],
            'domain': row['Domain'],
            'description': row['Description'],
            'retention_period': row['Retention period'],
            'data_controller': row['Data Controller'],
            'privacy_portals': row['User Privacy & GDPR Rights Portals'],
            'wildcard_match': int(row['Wildcard match'])
        }
        
        if database.insert_opencookiedb_record(record):
            num_success += 1
        else:
            num_failed += 1
            
    return num_success, num_failed

if __name__ == "__main__":
    print("Scaricamento del database dei cookie...")
    df = download_cookie_database()
    if df is not None:
        print(f"Database scaricato correttamente! Salvato in open-cookiedb.csv")
        print(f"Trovati {len(df)} cookie nel database")
        
        print("Importazione nel database...")
        success, failed = import_to_database(df)
        print(f"Importazione completata: {success} record importati, {failed} falliti")
