from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import ActionChains
import time

import chromedriver_autoinstaller

def setup_driver():
    """Configura e restituisce un'istanza del driver Chrome"""
    # Verifica e installa la versione corretta di chromedriver
    chromedriver_autoinstaller.install()
    
    chrome_options = Options()
    # Create a unique user data directory using timestamp
    import tempfile
    import os
    import random

    # Crea una directory unica per i dati utente di Chrome
    timestamp = int(time.time() * 1000)  # Timestamp in millisecondi per maggiore unicità
    random_id = random.randint(1000, 9999)
    user_data_dir = os.path.join(tempfile.gettempdir(), f"chrome_profile_{timestamp}_{random_id}")

    # Pulisci eventuali directory vecchie (più di 1 ora)
    temp_dir = tempfile.gettempdir()
    current_time = time.time()
    for item in os.listdir(temp_dir):
        if item.startswith("chrome_profile_"):
            item_path = os.path.join(temp_dir, item)
            if os.path.isdir(item_path):
                try:
                    # Controlla se la directory è più vecchia di 1 ora
                    if current_time - os.path.getctime(item_path) > 3600:
                        import shutil
                        shutil.rmtree(item_path, ignore_errors=True)
                except:
                    pass  # Ignora errori di pulizia

    os.makedirs(user_data_dir, exist_ok=True)
    
    chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
    chrome_options.add_argument("--window-size=1280,800")
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-notifications")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--lang=it")

    # Opzioni specifiche per ambienti containerizzati/Docker
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-images")
    chrome_options.add_argument("--disable-translate")
    chrome_options.add_argument("--hide-scrollbars")
    chrome_options.add_argument("--mute-audio")
    chrome_options.add_argument("--disable-background-timer-throttling")
    chrome_options.add_argument("--disable-backgrounding-occluded-windows")
    chrome_options.add_argument("--disable-ipc-flooding-protection")

    # Opzioni per la sicurezza in container
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")
    chrome_options.add_argument("--remote-debugging-port=9222")

    # Opzioni aggiuntive per evitare conflitti
    chrome_options.add_argument("--disable-dev-tools")
    chrome_options.add_argument("--disable-crash-reporter")
    chrome_options.add_argument("--log-level=3")
    chrome_options.add_argument("--disable-gpu-sandbox")
    chrome_options.add_argument("--disable-software-rasterizer")
    chrome_options.add_argument("--disable-background-networking")
    chrome_options.add_argument("--disable-default-apps")
    chrome_options.add_argument("--disable-extensions-http-throttling")
    chrome_options.add_argument("--disable-logging")
    chrome_options.add_argument("--disable-plugins")
    chrome_options.add_argument("--disable-popup-blocking")
    chrome_options.add_argument("--disable-prompt-on-repost")
    chrome_options.add_argument("--disable-renderer-backgrounding")
    chrome_options.add_argument("--disable-sync")
    chrome_options.add_argument("--force-color-profile=srgb")
    chrome_options.add_argument("--metrics-recording-only")
    chrome_options.add_argument("--no-first-run")
    chrome_options.add_argument("--safebrowsing-disable-auto-update")
    chrome_options.add_argument("--enable-automation")
    chrome_options.add_argument("--password-store=basic")
    chrome_options.add_argument("--use-mock-keychain")

    # Gestione della memoria
    chrome_options.add_argument("--memory-pressure-off")
    chrome_options.add_argument("--max_old_space_size=4096")
    
    # Configurazioni per il logging dei cookie
    chrome_options.set_capability("goog:loggingPrefs", {'performance': 'ALL'})
    
    # Configurazione del servizio con keep_alive
    service = Service(keep_alive=True)
    
    # Configura il logging di urllib3
    import logging
    logging.getLogger('urllib3.connectionpool').setLevel(logging.ERROR)
    
    # Aggiungi altre opzioni se necessario
    try:
        driver = webdriver.Chrome(options=chrome_options, service=service)

        # Imposta timeout più lunghi
        driver.implicitly_wait(10)
        driver.set_page_load_timeout(30)
        driver.set_script_timeout(30)

        # Test di connessione
        print("Browser avviato con successo, test di connessione...")
        driver.get("data:text/html,<html><body><h1>Test</h1></body></html>")
        print("Test di connessione completato.")

        return driver
    except Exception as e:
        print(f"Errore durante l'avvio del browser: {str(e)}")
        print("Assicurati che Google Chrome sia installato correttamente.")

        # Prova a stampare informazioni di debug
        import subprocess
        try:
            result = subprocess.run(['google-chrome', '--version'], capture_output=True, text=True)
            print(f"Versione Chrome: {result.stdout}")
        except:
            print("Impossibile ottenere la versione di Chrome")

        raise e

def check_cookie_banner(driver):
    """Cerca e gestisce il banner dei cookie"""
    banner_selectors = [
        ('id', 'cookie-banner'),
        ('css selector', '.cookie-notice'),
        ('css selector', '.gdpr-banner'),
        ('css selector', '.cookie-consent'),
        ('css selector', '.cookie-popup'),
        ('css selector', '.cookie-alert'),
        ('css selector', '.cookie-container'),
        ('css selector', '.cookie-modal'),
        ('css selector', '.cookie-dialog'),
        ('css selector', '.cookie-overlay'),
    ]
    
    for by, selector in banner_selectors:
        try:
            banner = driver.find_element(by, selector)
            print("\nBanner dei cookie rilevato!")
            print("1. Accetta tutti i cookie")
            print("2. Rifiuta i cookie non necessari")
            print("3. Personalizza le preferenze")
            choice = input("Seleziona un'opzione (1/2/3): ")
            
            if choice == '1':
                accept_button = banner.find_element(By.CSS_SELECTOR, '.accept-btn, .btn-accept, .cookie-accept')
                accept_button.click()
            elif choice == '2':
                reject_button = banner.find_element(By.CSS_SELECTOR, '.reject-btn, .btn-reject, .cookie-reject')
                reject_button.click()
            elif choice == '3':
                settings_button = banner.find_element(By.CSS_SELECTOR, '.settings-btn, .btn-settings, .cookie-settings')
                settings_button.click()
                time.sleep(2)  # Attendi che si apra il pannello
                handle_cookie_settings(driver)
            
            time.sleep(1)  # Attendi che il banner scompaia
            return True
        except:
            continue
    
    return False

def handle_cookie_settings(driver):
    """Gestisce le impostazioni avanzate dei cookie"""
    try:
        # Esempio: disabilita i cookie di tracciamento
        tracking_toggle = driver.find_element(By.CSS_SELECTOR, '.tracking-toggle')
        if tracking_toggle.is_selected():
            tracking_toggle.click()
        
        # Salva le preferenze
        save_button = driver.find_element(By.CSS_SELECTOR, '.save-preferences')
        save_button.click()
        return True
    except Exception as e:
        print(f"Errore nella gestione delle preferenze: {str(e)}")
        return False

def collect_cookies(driver):
    """Raccoglie i cookie attualmente presenti"""
    try:
        if not driver or not hasattr(driver, 'get_cookies'):
            return []
            
        # Forza l'aggiornamento dei cookie
        driver.execute_script("return document.cookie;")
        
        # Attendi un momento per permettere ai cookie di terze parti di essere impostati
        time.sleep(1)
        
        cookies = driver.get_cookies()
        processed_cookies = []
        
        for cookie in cookies:
            processed_cookie = {
                'name': cookie.get('name', ''),
                'value': cookie.get('value', ''),
                'domain': cookie.get('domain', ''),
                'path': cookie.get('path', '/'),
                'expiry': cookie.get('expiry', 'Sessione'),
                'http_only': cookie.get('httpOnly', False),
                'secure': cookie.get('secure', False),
                'same_site': cookie.get('sameSite', 'Lax'),
                'page_url': driver.current_url,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                'is_third_party': cookie.get('domain') not in driver.current_url
            }
            processed_cookies.append(processed_cookie)
        
        return processed_cookies
    except Exception as e:
        print(f"Errore durante la raccolta dei cookie: {str(e)}")
        return []
