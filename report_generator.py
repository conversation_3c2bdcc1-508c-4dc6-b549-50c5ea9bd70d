import sqlite3
from pathlib import Path
import pandas as pd
from datetime import datetime
import markdown2
from cookie_analyzer import analyze_cookie
from database import insert_cookie_if_not_exists, get_base_cookie_name

DB_PATH = Path('data/cookies.db')

def get_friendly_duration(expiry):
    """Converte la scadenza in una durata leggibile"""
    if expiry == 'Sessione':
        return 'Sessione'
    try:
        # Prima prova a convertire da timestamp (numero intero)
        if isinstance(expiry, (int, float)) or (isinstance(expiry, str) and expiry.isdigit()):
            # Converti da timestamp a datetime
            exp_timestamp = float(expiry)
            exp_date = datetime.fromtimestamp(exp_timestamp)
        else:
            # Altrimenti prova a convertire da stringa formattata
            try:
                exp_date = datetime.strptime(expiry, "%Y-%m-%d %H:%M:%S")
            except:
                # Se fallisce, prova altri formati comuni
                try:
                    exp_date = datetime.strptime(expiry, "%Y-%m-%d")
                except:
                    return "Formato data sconosciuto"
        
        delta = exp_date - datetime.now()
        days = delta.days
        hours = delta.seconds // 3600
        
        if days < 0:
            return "Scaduto"
        elif days == 0:
            if hours == 0:
                return "Meno di 1 ora"
            elif hours == 1:
                return "1 ora"
            else:
                return f"{hours} ore"
        elif days == 1:
            return "1 giorno"
        else:
            return f"{days} giorni"
    except Exception as e:
        # Se tutto fallisce, restituisci il valore originale
        return f"Non convertibile ({expiry})"

def get_cookie_purpose(cookie_name):
    """Determina la finalità del cookie in base al nome"""
    name = cookie_name.lower()
    if any(x in name for x in ['session', 'auth', 'login', 'token']):
        return "Autenticazione utente"
    elif any(x in name for x in ['consent', 'gdpr', 'cookie']):
        return "Gestione preferenze cookie"
    elif any(x in name for x in ['cart', 'basket', 'order']):
        return "Gestione carrello"
    elif any(x in name for x in ['track', 'analytics', 'utm']):
        return "Tracciamento e analisi"
    elif any(x in name for x in ['pref', 'settings', 'config']):
        return "Preferenze utente"
    return "Da determinare"

def get_cookie_category(cookie_name):
    """Classifica il cookie in base al nome"""
    name = cookie_name.lower()
    if any(x in name for x in ['_ga', '_gid', '_gat', 'utm']):
        return "Analytics"
    elif any(x in name for x in ['_fbp', 'fr', 'tr']):
        return "Marketing"
    elif any(x in name for x in ['cookie', 'consent', 'gdpr']):
        return "Funzionale"
    elif any(x in name for x in ['session', 'auth', 'token']):
        return "Tecnico"
    return "Da classificare"

def get_cookie_info_from_db(cookie_name):
    """Recupera le informazioni sul cookie dalla tabella opencookiedb."""
    base_name = get_base_cookie_name(cookie_name)
    with sqlite3.connect(DB_PATH) as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT description, category FROM opencookiedb WHERE id = ?", (base_name,))
        result = cursor.fetchone()
        if result:
            return result[0], result[1]
        # Se non trovato per nome base, cerca per nome esatto
        cursor.execute("SELECT description, category FROM opencookiedb WHERE cookie_name = ?", (cookie_name,))
        result = cursor.fetchone()
        if result:
            return result[0], result[1]
    return None, None

def generate_markdown_report(report_dir, timestamp, cookies_data, visited_pages, base_url):
    """Genera un report markdown per il cliente"""
    md_file = report_dir / f"client_report_{timestamp}.md"
    
    # Filtra i cookie unici
    unique_cookies = []
    seen_cookies = set()
    
    for cookie in cookies_data:
        cookie_key = (cookie['name'], cookie['domain'], cookie['path'])
        if cookie_key not in seen_cookies:
            seen_cookies.add(cookie_key)
            unique_cookies.append(cookie)
    
    with open(md_file, 'w', encoding='utf-8') as f:
        # Intestazione
        f.write(f"# Report Analisi Cookie\n\n")
        # Converti la data da YYYYMMDD_HHMMSS a dd-mm-YYYY HH:MM
        report_date = f"{timestamp[6:8]}-{timestamp[4:6]}-{timestamp[:4]} {timestamp[9:11]}:{timestamp[11:13]}"
        f.write(f"**Data analisi:** {report_date}\n\n")
        f.write(f"**URL analizzato:** {base_url}\n\n")
        f.write(f"**Pagine analizzate:** {len(visited_pages)}\n\n")
        f.write(f"**Cookie rilevati:** {len(unique_cookies)}\n\n")
        f.write(f"**Cookie di terze parti:** {sum(1 for c in unique_cookies if c['is_third_party'])}\n\n")
        
        # Tabella riassuntiva
        f.write("## Riepilogo Cookie\n\n")
        f.write("| Nome | Dominio | Durata | Sicuro | HTTP Only | SameSite | Terze parti | Categoria | Descrizione |\n")
        f.write("|------|---------|--------|--------|-----------|----------|-------------|-----------|-------------|\n")
        for cookie in cookies_data:
            db_description, db_category = get_cookie_info_from_db(cookie['name'])
            if not db_description or not db_category:
                print(f"Cookie '{cookie['name']}' non trovato nel database. Avvio analisi con ChatGPT...")
                analysis_result = analyze_cookie(cookie['name'])
                if analysis_result and analysis_result['purpose'] != "Da determinare":
                    insert_cookie_if_not_exists(
                        analysis_result['cookie_name'],
                        analysis_result['category'],
                        analysis_result['description'],
                        analysis_result.get('platform', ''),
                        None  # Leave domain empty for ChatGPT-analyzed cookies
                    )
                    db_description = analysis_result['description']
                    db_category = analysis_result['category']
                else:
                    db_description = "Nessuna descrizione disponibile tramite analisi."
                    db_category = "Da classificare"
            category = db_category if db_category else get_cookie_category(cookie['name'])
            description = db_description if db_description else "Nessuna descrizione disponibile."
            f.write(f"| {cookie['name']} | {cookie['domain']} | {get_friendly_duration(cookie['expiry'])} | {'✔️' if cookie['secure'] else '❌'} | {'✔️' if cookie['http_only'] else '❌'} | {cookie['same_site']} | {'✔️' if cookie['is_third_party'] else '❌'} | {category} | {description[:50]} |\n")
        f.write("\n")
        
        # Dettaglio cookie
        f.write("## Dettaglio Cookie\n\n")
        for i, cookie in enumerate(cookies_data, 1):
            # Get fresh description and category for each cookie
            db_description, db_category = get_cookie_info_from_db(cookie['name'])
            if not db_description or not db_category:
                analysis_result = analyze_cookie(cookie['name'])
                if analysis_result and analysis_result['purpose'] != "Da determinare":
                    insert_cookie_if_not_exists(
                        analysis_result['cookie_name'],
                        analysis_result['category'],
                        analysis_result['description'],
                        analysis_result.get('platform', ''),
                        None
                    )
                    db_description = analysis_result['description']
                    db_category = analysis_result['category']
                else:
                    db_description = "Nessuna descrizione disponibile tramite analisi."
                    db_category = "Da classificare"
            f.write(f"### Cookie {i}: {cookie['name']}\n\n")
            f.write(f"- **Dominio:** {cookie['domain']}\n")
            f.write(f"- **Percorso:** {cookie['path']}\n")
            f.write(f"- **Durata:** {get_friendly_duration(cookie['expiry'])}\n")
            f.write(f"- **Sicuro:** {'Sì (trasmissione solo HTTPS)' if cookie['secure'] else 'No (trasmissione anche HTTP)'}\n")
            f.write(f"- **HTTP Only:** {'Sì (non accessibile via JavaScript)' if cookie['http_only'] else 'No (accessibile via JavaScript)'}\n")
            f.write(f"- **SameSite:** {cookie['same_site']}\n")
            f.write(f"- **Valore:** `{cookie['value']}`\n")
            f.write(f"- **Finalità:** {get_cookie_purpose(cookie['name'])}\n")
            if db_category:
                f.write(f"- **Categoria OpenCookieDB:** {db_category}\n")
            else:
                f.write(f"- **Categoria:** {get_cookie_category(cookie['name'])}\n")
            f.write(f"- **Descrizione OpenCookieDB:** {db_description}\n\n")
            f.write("---\n\n")

def generate_reports(cookies_data, actions_log, visited_pages, base_url):
    """Genera i report tecnici e per il cliente"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Normalizza l'URL per creare il percorso
    clean_url = base_url.replace('https://', '').replace('http://', '').replace('www.', '')
    if '/' in clean_url:
        clean_url = clean_url.split('/')[0]
    
    # Crea la struttura delle cartelle
    report_dir = Path('reports') / clean_url / timestamp
    report_dir.mkdir(parents=True, exist_ok=True)
    
    # Filtra i cookie unici
    unique_cookies = []
    seen_cookies = set()
    
    for cookie in cookies_data:
        cookie_key = (cookie['name'], cookie['domain'], cookie['path'])
        if cookie_key not in seen_cookies:
            seen_cookies.add(cookie_key)
            unique_cookies.append(cookie)
    
    # Report tecnico
    tech_report = {
        'timestamp': timestamp,
        'total_cookies': len(unique_cookies),
        'unique_domains': len(set(c['domain'] for c in unique_cookies)),
        'pages_visited': list(visited_pages),
        'actions': actions_log,
        'cookies': unique_cookies
    }
    
    # Salva report tecnico
    tech_df = pd.DataFrame(tech_report['cookies'])
    tech_df.to_csv(report_dir / f"tech_report_{timestamp}.csv", index=False)
    
    with open(report_dir / f"tech_log_{timestamp}.txt", "w") as f:
        f.write(f"Analisi completata il: {timestamp}\n")
        f.write(f"Pagine visitate: {len(visited_pages)}\n")
        f.write(f"Cookie totali raccolti: {len(cookies_data)}\n")
        f.write("\nLog delle azioni:\n")
        f.write("\n".join(actions_log))
    
    # Report per il cliente
    client_data = []
    for cookie in cookies_data:
        db_description, db_category = get_cookie_info_from_db(cookie['name'])
        category = db_category if db_category else get_cookie_category(cookie['name'])
        description = db_description if db_description else "Nessuna descrizione disponibile."
        client_data.append({
            'Nome': cookie['name'],
            'Dominio': cookie['domain'],
            'Durata': get_friendly_duration(cookie['expiry']),
            'Sicuro': 'Sì' if cookie['secure'] else 'No',
            'HTTP Only': 'Sì' if cookie['http_only'] else 'No',
            'SameSite': cookie['same_site'],
            'Finalità': get_cookie_purpose(cookie['name']),
            'Categoria': category,
            'Descrizione': description
        })
    
    client_df = pd.DataFrame(client_data)
    client_df.to_csv(report_dir / f"client_report_{timestamp}.csv", index=False)
    
    # Genera il report markdown
    generate_markdown_report(report_dir, timestamp, cookies_data, visited_pages, base_url)
    
    # Generate HTML version
    md_file = report_dir / f"client_report_{timestamp}.md"
    html_file = report_dir / f"client_report_{timestamp}.html"
    with open(md_file, 'r', encoding='utf-8') as md_input:
        html_content = markdown2.markdown(md_input.read(), extras=['tables'])
        
    html_template = f"""
    <!DOCTYPE html>
    <html lang="it">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Report Analisi Cookie</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                line-height: 1.6;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin: 20px 0;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
            h1, h2, h3 {{
                color: #333;
            }}
            hr {{
                border: none;
                border-top: 1px solid #ddd;
                margin: 20px 0;
            }}
            /* Stili per la durata dei cookie */
            td:nth-child(3) {{  /* Colonna della durata */
                font-weight: 500;
            }}
            .duration-expired {{
                color: #dc3545;
            }}
            .duration-session {{
                color: #0d6efd;
            }}
            .duration-days {{
                color: #198754;
            }}
            /* Stili per i dettagli dei cookie */
            .cookie-details {{
                margin: 20px 0;
                padding: 15px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }}
            .cookie-details p {{
                margin: 5px 0;
            }}
        </style>
    </head>
    <body>
        {html_content}
        <script>
            // Add color classes to duration cells
            document.addEventListener('DOMContentLoaded', function() {{
                const durationCells = document.querySelectorAll('table td:nth-child(3)');
                durationCells.forEach(cell => {{
                    const text = cell.textContent.trim();
                    if (text === 'Scaduto') {{
                        cell.classList.add('duration-expired');
                    }} else if (text === 'Sessione') {{
                        cell.classList.add('duration-session');
                    }} else if (text.includes('giorni') || text.includes('giorno')) {{
                        cell.classList.add('duration-days');
                    }}
                }});
            }});
        </script>
    </body>
    </html>
    """
    
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_template)

    print(f"\nReport generati con successo nella cartella: {report_dir}")
    print(f"- Report tecnico: tech_report_{timestamp}.csv")
    print(f"- Report per il cliente: client_report_{timestamp}.csv")
    print(f"- Documentazione completa: client_report_{timestamp}.md")
    print(f"- Versione HTML: client_report_{timestamp}.html")
