<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report <PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <style>
        .collapse-icon {
            transition: transform 0.3s ease;
        }
        .collapsed .collapse-icon {
            transform: rotate(-90deg);
        }
        .btn-danger.btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.2rem;
        }
        .card-header h3 {
            font-size: 1.2rem;
            margin: 0;
        }
        /* Nuovi stili per i badge dei report */
        .badge {
            padding: 0.4em 0.8em;
            margin-left: 8px;
        }
        .badge-technical {
            background-color: #17a2b8;
            color: white;
        }
        .badge-client {
            background-color: #28a745;
            color: white;
        }
        .badge-html {
            background-color: #fd7e14;
            color: white;
        }
        .badge-markdown {
            background-color: #6610f2;
            color: white;
        }
        /* Stili per i link dei report */
        .report-link {
            display: flex;
            justify-content: space-between;
            align-items: center;
            text-decoration: none;
            color: #333;
            padding: 8px 0;
        }
        .report-link:hover {
            color: #007bff;
            text-decoration: none;
        }
        /* Stili per le card */
        .card {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid rgba(0,0,0,0.125);
        }
        .card-header {
            background-color: #f8f9fa;
            cursor: pointer;
        }
        .card-header:hover {
            background-color: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <a href="/" class="btn btn-link">← Torna alla pagina principale</a>
            {% if domains %}
            <form action="{{ url_for('delete_all_reports') }}" 
                  method="POST" 
                  onsubmit="return confirm('Sei sicuro di voler eliminare TUTTI i report? Questa azione non può essere annullata.');">
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> Elimina tutti i report
                </button>
            </form>
            {% endif %}
        </div>
        <h1>Report Analisi Cookie</h1>

        {% if not domains %}
            <div class="alert alert-warning text-center">
                <p>Nessun report disponibile.</p>
                <p>Esegui prima un'analisi dalla pagina principale.</p>
            </div>
        {% else %}
            {% for domain, timestamps in domains.items() %}
                <div class="card mb-3">
                    <div class="card-header" role="button" data-toggle="collapse" 
                         data-target="#collapse-{{ domain|replace('.', '-') }}" 
                         aria-expanded="false">
                        <div class="d-flex justify-content-between align-items-center">
                            <h2>{{ domain }}</h2>
                            <i class="fas fa-chevron-down collapse-icon"></i>
                        </div>
                    </div>
                    <div class="collapse" id="collapse-{{ domain|replace('.', '-') }}">
                        <div class="card-body">
                            {% for timestamp_data in timestamps %}
                                <div class="card mb-2">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h3 class="mb-0">Analisi del {{ timestamp_data.timestamp[6:8] }}-{{ timestamp_data.timestamp[4:6] }}-{{ timestamp_data.timestamp[:4] }}
                                            alle {{ timestamp_data.timestamp[9:11] }}:{{ timestamp_data.timestamp[11:13] }}</h3>
                                        <form action="{{ url_for('delete_analysis', domain=domain, timestamp=timestamp_data.timestamp) }}" 
                                              method="POST" 
                                              style="display: inline;"
                                              onsubmit="return confirm('Sei sicuro di voler eliminare questa analisi?');">
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i> Elimina
                                            </button>
                                        </form>
                                    </div>
                                    <ul class="list-group list-group-flush">
                                        {% for report in timestamp_data.reports %}
                                            <li class="list-group-item">
                                                <a href="{{ url_for('serve_report', filename=report.path) }}" 
                                                   class="report-link" target="_blank">
                                                    {{ report.filename }}
                                                    <span class="badge {% if 'tech' in report.type %}badge-technical
                                                      {% elif 'client' in report.type %}badge-client
                                                      {% elif 'html' in report.type %}badge-html
                                                      {% else %}badge-markdown{% endif %}">
                                                        {{ report.type }}
                                                    </span>
                                                </a>
                                            </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <!-- Aggiungi questi script prima della chiusura del body -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.card-header[data-toggle="collapse"]').click(function() {
                $(this).toggleClass('collapsed');
            });
        });
    </script>
</body>
</html>
